#!/usr/bin/env python3
"""
测试同花顺财经爬虫
"""

from crawler.Spider_THS_finance import Spider_THS_Finance

def test_spider():
    print("开始测试同花顺财经爬虫...")
    
    try:
        # 创建爬虫实例并运行
        spider = Spider_THS_Finance(1, None)
        article_list = spider.run()
        
        print(f"成功爬取到 {len(article_list)} 篇文章")
        
        # 显示前几篇文章的信息
        for i, article in enumerate(article_list[:3]):
            print(f"\n文章 {i+1}:")
            print(f"  标题: {article.title}")
            print(f"  发布时间: {article.published_at}")
            print(f"  来源: {article.source}")
            print(f"  分类: {article.cat}")
            print(f"  URL: {article.url}")
            print(f"  内容预览: {article.content[:100]}..." if article.content else "  内容: 无")
            
        print("\n爬虫测试完成！")
        return True
        
    except Exception as e:
        print(f"爬虫测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_spider()
